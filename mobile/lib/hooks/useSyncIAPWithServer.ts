import { useCallback, useEffect } from "react";
import { AppState, Platform } from "react-native";
import { Purchase, useIAP, getAvailablePurchases, finishTransaction, } from "react-native-iap";
import { API_URL } from "../constants";
import { CommonService } from "../services/common.service";
import { usePaymentErrorStore } from "../store";
import { useProductsConfig } from "./use-products-config";

const commonService = new CommonService();
export const useSyncIAPWithServer = () => {
    const productConfigs = useProductsConfig();
    const { showError } = usePaymentErrorStore();

    // Handle purchase updates
    const handlePurchase = useCallback(async (currentPurchase: Purchase) => {
        console.log('Processing purchase:', currentPurchase.productId);
        try {
            const platform = Platform.OS as 'ios' | 'android';
            const config = productConfigs.find(config =>
                config.productId[platform] === currentPurchase.productId
            )

            if (!config) {
                console.error('Unknown product purchased:', currentPurchase.productId);
                showError('Unknown product purchased. Please contact support if this issue persists.');
                return;
            }

            // Validate purchase with backend
            const headers = await commonService.setTokenInHeaders();
            const response = await fetch(`${API_URL}/payments/validate`, {
                method: 'POST',
                headers,
                body: JSON.stringify({
                    receipt: currentPurchase.transactionReceipt,
                    productId: currentPurchase.productId,
                    packageId: config.id,
                    platform: Platform.OS,
                    packageType: config.type === 'consumable' ? 'day_refill' : 'subscription',
                    originalTransactionId: currentPurchase.originalTransactionIdentifierIOS,
                    transactionId: currentPurchase.transactionId,
                    transactionDate: currentPurchase.transactionDate,
                    ...(config.type === 'subscription'
                        ? { daysPerMonth: config.daysPerMonth }
                        : { days: config.days }
                    ),
                }),
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || 'Purchase validation failed');
            }

            await response.json(); // Consume the response

            // Finish the transaction
            await finishTransaction({
                purchase: currentPurchase,
                isConsumable: config?.type === 'consumable',
            });

        } catch (error) {
            console.error('Purchase processing failed:', error);

            // Show user-friendly error message
            const errorMessage = error instanceof Error
                ? error.message
                : 'Purchase processing failed. Please try again.';

            showError(errorMessage);
        }
    }, [productConfigs, showError]);
    useEffect(() => {
        const sub = AppState
            .addEventListener(
                "change",
                () => {

                    getAvailablePurchases().then(purchases => {
                        console.log('---------- Available purchases:', purchases.length);
                        purchases.forEach(handlePurchase);
                    });
                });

        return () => {
            console.log('---------- Removing purchase listener');
            sub.remove()
        };
    }, [productConfigs]);
}